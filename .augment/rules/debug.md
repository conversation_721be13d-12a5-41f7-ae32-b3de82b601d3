---
type: "always_apply"
---

# GWHW网关MCP服务 - 调试和开发规则

本文档定义了GWHW网关MCP服务项目的调试、开发和部署规则，确保代码质量和项目一致性。

## 🔧 环境配置规则

### 1. Python虚拟环境激活
**强制要求**：在执行任何Python脚本之前，必须先激活虚拟环境：

```bash
# 激活虚拟环境
source .venv/bin/activate

# 然后执行Python脚本
python main.py
python demo_credential_management.py
python -m pytest tests/
```

**禁止**：直接使用系统Python执行项目脚本，这可能导致依赖冲突和版本不兼容问题。

### 2. Python版本兼容性
项目使用 **Python 3.12**，开发时需要注意：

- **Pydantic v2兼容性**：使用 `pattern=` 替代已废弃的 `regex=` 参数
- **类型注解**：使用现代Python类型注解语法
- **异步编程**：充分利用Python 3.12的异步特性
- **依赖版本**：确保所有依赖包与Python 3.12兼容

```python
# ✅ 正确：使用pattern参数
server_ip: str = Field(..., pattern=r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")

# ❌ 错误：使用已废弃的regex参数
server_ip: str = Field(..., regex=r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")
```

## 🚀 新会话开发规则

### 3. 项目理解要求
开始新的开发会话时，**必须**按以下顺序进行：

1. **阅读项目文档**：
   ```bash
   # 首先阅读项目README
   cat README.md
   ```

2. **了解项目架构**：
   - FastAPI + Redis + SSH服务技术栈
   - 分层架构：API层 → 服务层 → 工具层
   - 凭据管理系统的安全设计
   - 异步任务管理机制

3. **查看代码结构**：
   ```
   gwhw_mcp/
   ├── api/                    # API接口层
   ├── config/                 # 配置管理
   ├── core/                   # 核心功能
   ├── models/                 # 数据模型
   ├── services/               # 业务服务层
   ├── utils/                  # 工具模块
   └── tests/                  # 测试模块
   ```

4. **理解核心功能**：
   - SSH服务集成和凭据自动填充
   - Redis存储架构和任务管理
   - 加密工具和安全策略
   - 构建、部署、测试服务

## 💻 代码开发规则

### 4. 依赖管理
**使用包管理器**安装依赖，禁止手动编辑requirements.txt：

```bash
# ✅ 正确：使用pip安装
pip install cryptography==41.0.7

# ✅ 正确：使用pip卸载
pip uninstall package-name

# ❌ 错误：手动编辑requirements.txt
```

### 5. 测试要求
**强制要求**：新功能开发完成后必须编写测试：

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_credential_service.py

# 运行测试并显示覆盖率
pytest --cov=services --cov=utils tests/
```

**测试覆盖范围**：
- 单元测试：测试单个函数和方法
- 集成测试：测试服务间交互
- 边界条件测试：测试异常情况和边界值

### 6. 安全考虑
涉及凭据管理时的**强制安全要求**：

- **密码加密**：所有密码必须使用AES-256加密存储
- **密钥管理**：加密密钥通过环境变量配置，不得硬编码
- **审计日志**：记录所有凭据操作的审计日志
- **权限控制**：API接口不得返回明文密码

```python
# ✅ 正确：加密存储密码
encrypted_password = CryptoUtils.encrypt_password(password, key)

# ❌ 错误：明文存储密码
credential.password = plain_password
```

### 7. 向后兼容性
修改现有功能时**必须保持**API向后兼容性：

- 新增可选参数而非修改必需参数
- 保持现有API端点的响应格式
- 添加新功能时不影响现有功能
- 使用版本控制管理API变更

### 8. 错误处理
**统一使用**项目定义的自定义异常类：

```python
# ✅ 正确：使用自定义异常
from core.exceptions import CredentialNotFoundException
raise CredentialNotFoundException(server_ip)

# ❌ 错误：使用通用异常
raise Exception("Credential not found")
```

## 🔍 调试和部署规则

### 9. 日志记录
**强制使用**项目统一的日志系统：

```python
from core.logging import get_logger
logger = get_logger(__name__)

# ✅ 正确：结构化日志记录
logger.info(f"创建服务器凭据: {server_ip}")
logger.error(f"凭据创建失败 {server_ip}: {e}")

# ❌ 错误：使用print输出
print(f"Creating credential for {server_ip}")
```

**日志级别使用规范**：
- `DEBUG`：详细的调试信息
- `INFO`：一般操作信息
- `WARNING`：警告信息
- `ERROR`：错误信息
- `CRITICAL`：严重错误

### 10. 配置管理
新增配置项时**必须同时更新**：

1. `config/settings.py` - 添加配置项定义
2. `README.md` - 更新配置说明
3. `config.example` - 添加配置示例

```python
# settings.py中添加配置
new_feature_enabled: bool = True

# README.md中添加说明
# 新功能配置
NEW_FEATURE_ENABLED=true  # 是否启用新功能
```

### 11. 文档更新
功能变更后**必须及时更新**相关文档：

- API变更：更新README.md中的API文档
- 配置变更：更新配置说明和示例
- 架构变更：更新架构图和说明
- 新功能：添加使用示例和最佳实践

## 🏗️ 架构最佳实践

### Redis连接管理
- 使用连接池管理Redis连接
- 实现连接重试和错误恢复机制
- 合理设置键的过期时间
- 使用事务确保数据一致性

### SSH服务集成
- 支持密码和密钥两种认证方式
- 实现连接超时和重试机制
- 自动凭据获取和回退策略
- 文件传输的完整性验证

### 异步任务处理
- 使用TaskManager统一管理任务
- 实现任务状态跟踪和生命周期管理
- 支持任务的创建、查询、更新和删除
- 提供任务清理和历史记录功能

### 服务分层设计
- API层：处理HTTP请求和响应
- 服务层：实现业务逻辑
- 工具层：提供通用功能
- 模型层：定义数据结构

## 🚨 常见问题和解决方案

### 1. Pydantic版本兼容性问题
**问题**：`regex` 参数已被废弃
**解决**：使用 `pattern` 参数替代

### 2. Redis连接失败
**问题**：Redis服务不可用
**解决**：检查Redis服务状态和连接配置

### 3. SSH连接超时
**问题**：SSH连接或命令执行超时
**解决**：调整超时设置，检查网络连通性

### 4. 凭据加密失败
**问题**：加密密钥无效或丢失
**解决**：重新生成密钥，恢复备份数据

## 📋 开发检查清单

开发新功能时，请确保完成以下检查：

- [ ] 激活虚拟环境
- [ ] 阅读相关文档和代码
- [ ] 遵循项目架构和设计模式
- [ ] 实现完整的错误处理
- [ ] 添加适当的日志记录
- [ ] 编写单元测试和集成测试
- [ ] 更新相关文档
- [ ] 验证向后兼容性
- [ ] 进行安全审查（如涉及凭据管理）
- [ ] 测试在不同环境下的运行情况

遵循这些规则将确保项目的代码质量、安全性和可维护性。