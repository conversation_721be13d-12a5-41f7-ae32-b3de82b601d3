"""任务相关数据模型

定义任务的数据结构。
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Any
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    ERROR = "error"


class BaseTask(BaseModel):
    """基础任务模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    message: str = Field(..., description="任务消息")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    class Config:
        use_enum_values = True


class BuildTask(BaseTask):
    """构建任务模型"""
    remote_ip: str = Field(..., description="远程服务器IP")
    ssh_user: str = Field(..., description="SSH用户名")
    container_name: str = Field(..., description="容器名称")
    
    # 执行结果
    stdout: Optional[str] = Field(None, description="标准输出")
    stderr: Optional[str] = Field(None, description="错误输出")
    return_code: Optional[int] = Field(None, description="返回码")
    
    # 部署相关字段（用于deploy任务）
    plugin_name: Optional[str] = Field(None, description="插件名称")
    container_plugin_path: Optional[str] = Field(None, description="容器插件路径")
    server_plugin_path: Optional[str] = Field(None, description="服务器插件路径")
    
    # 插件编译相关字段
    plugin_path: Optional[str] = Field(None, description="插件源码路径")
    make_jobs: Optional[int] = Field(None, description="make并行任务数")


class TestTask(BaseTask):
    """测试任务模型"""
    pcap_file_path: Optional[str] = Field(None, description="pcap文件路径")
    filename: Optional[str] = Field(None, description="上传的文件名")
    remote_ip: str = Field(..., description="远程服务器IP")
    ssh_user: str = Field(..., description="SSH用户名")
    wait_seconds: int = Field(..., description="等待处理时间")
    log_lines: int = Field(..., description="日志输出行数")
    eth_name: Optional[str] = Field(None, description="网口名称（用于tcpreplay）")
    
    # 测试结果
    hw_log: Optional[str] = Field(None, description="网关处理日志")
    hw_err: Optional[str] = Field(None, description="网关错误日志")
    json_events: Optional[list[dict[str, Any]]] = Field(None, description="JSON事件数据")